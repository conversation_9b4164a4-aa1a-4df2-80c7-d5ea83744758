{"generation_time": "2025-08-07T09:12:24.113693", "best_model": "CatBoost", "best_score": 0.8848230324205927, "model_count": 10, "detailed_metrics": {"DecisionTree": {"accuracy": 0.8048780487804879, "precision": 0.8125, "recall": 0.7222222222222222, "f1_score": 0.7647058823529411, "specificity": 0.8695652173913043, "sensitivity": 0.7222222222222222, "npv": 0.8, "ppv": 0.8125, "auc_roc": 0.8997584541062802, "auc_pr": 0.8136981443140863, "mcc": 0.6020546542991633, "kappa": 0.5990220048899756, "balanced_accuracy": 0.7958937198067633, "composite_score": 0.7741411060871253}, "RandomForest": {"accuracy": 0.9, "precision": 0.9333333333333333, "recall": 0.8235294117647058, "f1_score": 0.875, "specificity": 0.9565217391304348, "sensitivity": 0.8235294117647058, "npv": 0.88, "ppv": 0.9333333333333333, "auc_roc": 0.9386189258312021, "auc_pr": 0.9390970495829831, "mcc": 0.7965184258559546, "kappa": 0.7922077922077921, "balanced_accuracy": 0.8900255754475703, "composite_score": 0.8807309608093395}, "XGBoost": {"accuracy": 0.8780487804878049, "precision": 0.8823529411764706, "recall": 0.8333333333333334, "f1_score": 0.8571428571428571, "specificity": 0.9130434782608695, "sensitivity": 0.8333333333333334, "npv": 0.875, "ppv": 0.8823529411764706, "auc_roc": 0.9685990338164252, "auc_pr": 0.9604842284237785, "mcc": 0.7518448466849966, "kappa": 0.7509113001215066, "balanced_accuracy": 0.8731884057971014, "composite_score": 0.8669853634442473}, "LightGBM": {"accuracy": 0.8780487804878049, "precision": 0.8823529411764706, "recall": 0.8333333333333334, "f1_score": 0.8571428571428571, "specificity": 0.9130434782608695, "sensitivity": 0.8333333333333334, "npv": 0.875, "ppv": 0.8823529411764706, "auc_roc": 0.9492753623188406, "auc_pr": 0.9480097901293554, "mcc": 0.7518448466849966, "kappa": 0.7509113001215066, "balanced_accuracy": 0.8731884057971014, "composite_score": 0.8631206291447302}, "CatBoost": {"accuracy": 0.9, "precision": 0.9333333333333333, "recall": 0.8235294117647058, "f1_score": 0.875, "specificity": 0.9565217391304348, "sensitivity": 0.8235294117647058, "npv": 0.88, "ppv": 0.9333333333333333, "auc_roc": 0.959079283887468, "auc_pr": 0.9570278637770897, "mcc": 0.7965184258559546, "kappa": 0.7922077922077921, "balanced_accuracy": 0.8900255754475703, "composite_score": 0.8848230324205927}, "Logistic": {"accuracy": 0.8536585365853658, "precision": 0.875, "recall": 0.7777777777777778, "f1_score": 0.8235294117647058, "specificity": 0.9130434782608695, "sensitivity": 0.7777777777777778, "npv": 0.84, "ppv": 0.875, "auc_roc": 0.9323671497584541, "auc_pr": 0.9356070972450282, "mcc": 0.7028066576716763, "kappa": 0.6992665036674817, "balanced_accuracy": 0.8454106280193237, "composite_score": 0.8325657581098549}, "SVM": {"accuracy": 0.8, "precision": 0.8461538461538461, "recall": 0.6470588235294118, "f1_score": 0.7333333333333333, "specificity": 0.9130434782608695, "sensitivity": 0.6470588235294118, "npv": 0.7777777777777778, "ppv": 0.8461538461538461, "auc_roc": 0.9207161125319694, "auc_pr": 0.9082446119142531, "mcc": 0.5911561035156879, "kappa": 0.5778364116094987, "balanced_accuracy": 0.7800511508951407, "composite_score": 0.7634652051529024}, "NeuralNet": {"accuracy": 0.8780487804878049, "precision": 0.8823529411764706, "recall": 0.8333333333333334, "f1_score": 0.8571428571428571, "specificity": 0.9130434782608695, "sensitivity": 0.8333333333333334, "npv": 0.875, "ppv": 0.8823529411764706, "auc_roc": 0.961352657004831, "auc_pr": 0.9511205414285125, "mcc": 0.7518448466849966, "kappa": 0.7509113001215066, "balanced_accuracy": 0.8731884057971014, "composite_score": 0.8655360880819283}, "NaiveBayes": {"accuracy": 0.8780487804878049, "precision": 0.9333333333333333, "recall": 0.7777777777777778, "f1_score": 0.8484848484848485, "specificity": 0.9565217391304348, "sensitivity": 0.7777777777777778, "npv": 0.8461538461538461, "ppv": 0.9333333333333333, "auc_roc": 0.9057971014492754, "auc_pr": 0.9187531631009891, "mcc": 0.7565560516799671, "kappa": 0.7478474784747847, "balanced_accuracy": 0.8671497584541064, "composite_score": 0.8527137814786573}, "KNN": {"accuracy": 0.875, "precision": 0.875, "recall": 0.8235294117647058, "f1_score": 0.8484848484848485, "specificity": 0.9130434782608695, "sensitivity": 0.8235294117647058, "npv": 0.875, "ppv": 0.875, "auc_roc": 0.9322250639386189, "auc_pr": 0.9188618925831202, "mcc": 0.7432561251138006, "kappa": 0.7422680412371134, "balanced_accuracy": 0.8682864450127876, "composite_score": 0.8536598130164695}}, "ranking": [["CatBoost", 0.8848230324205927], ["RandomForest", 0.8807309608093395], ["XGBoost", 0.8669853634442473], ["NeuralNet", 0.8655360880819283], ["LightGBM", 0.8631206291447302], ["KNN", 0.8536598130164695], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", 0.8527137814786573], ["Logistic", 0.8325657581098549], ["DecisionTree", 0.7741411060871253], ["SVM", 0.7634652051529024]]}